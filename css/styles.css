/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-pink: #ffc1cc;
    --secondary-pink: #ffb3ba;
    --accent-gold: #ffd700;
    --soft-white: #fefefe;
    --warm-white: #fff8f0;
    --text-dark: #4a4a4a;
    --text-light: #666;
    --shadow-soft: 0 10px 30px rgba(255, 193, 204, 0.3);
    --shadow-medium: 0 15px 40px rgba(255, 193, 204, 0.4);
    --gradient-romantic: linear-gradient(135deg, #ffc1cc 0%, #ffb3ba 50%, #ffd1dc 100%);
    --gradient-proposal: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--gradient-romantic);
    min-height: 100vh;
    overflow-x: hidden;
    color: var(--text-dark);
}

/* Page Management */
.page {
    min-height: 100vh;
    display: none;
    position: relative;
    overflow: hidden;
}

.page.active {
    display: block;
}

/* Landing Page Styles */
#landing-page {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.romantic-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.landing-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    padding: 2rem;
}

.main-title {
    font-family: 'Dancing Script', cursive;
    font-size: 4rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8);
    animation: fadeInUp 1s ease-out;
}

.subtitle {
    font-family: 'Playfair Display', serif;
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Envelope Styles */
.envelope-container {
    margin: 3rem 0;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.envelope {
    position: relative;
    width: 200px;
    height: 140px;
    margin: 0 auto;
    cursor: pointer;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
}

.envelope:hover {
    transform: translateY(-10px) scale(1.05);
    filter: drop-shadow(var(--shadow-medium));
}

.envelope-body {
    width: 100%;
    height: 100px;
    background: var(--soft-white);
    border: 2px solid var(--primary-pink);
    border-radius: 8px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-soft);
}

.envelope-flap {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    height: 60px;
    background: var(--primary-pink);
    border: 2px solid var(--primary-pink);
    border-radius: 8px 8px 0 0;
    transform-origin: bottom;
    transition: transform 0.5s ease;
    z-index: 1;
    pointer-events: none; /* Allow clicks to pass through to the envelope container */
}

.envelope:hover .envelope-flap {
    transform: rotateX(-180deg);
}

.heart-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    animation: heartbeat 2s ease-in-out infinite;
}

.envelope-text {
    font-family: 'Playfair Display', serif;
    font-size: 0.9rem;
    color: var(--text-dark);
    font-weight: 500;
}

/* Decorative Elements */
.decorative-elements {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    pointer-events: none;
}

.letter-graphic {
    position: absolute;
    width: 60px;
    height: 80px;
    background: var(--soft-white);
    border: 2px solid var(--secondary-pink);
    border-radius: 4px;
    opacity: 0.7;
    animation: float 3s ease-in-out infinite;
}

.letter-graphic.left {
    left: 10%;
    animation-delay: 0s;
}

.letter-graphic.right {
    right: 10%;
    animation-delay: 1.5s;
}

.letter-graphic::before {
    content: '💕';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
}

/* Floating Hearts Animation */
.floating-hearts {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.floating-hearts::before,
.floating-hearts::after {
    content: '💕';
    position: absolute;
    font-size: 1.5rem;
    animation: floatUp 8s linear infinite;
    opacity: 0.6;
}

.floating-hearts::before {
    left: 20%;
    animation-delay: 0s;
}

.floating-hearts::after {
    left: 80%;
    animation-delay: 4s;
}

/* Sparkles Animation */
.sparkles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.sparkles::before,
.sparkles::after {
    content: '✨';
    position: absolute;
    animation: sparkle 3s ease-in-out infinite;
}

.sparkles::before {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.sparkles::after {
    top: 70%;
    right: 15%;
    animation-delay: 1.5s;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes floatUp {
    0% {
        bottom: -50px;
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        bottom: 100vh;
        opacity: 0;
    }
}

@keyframes sparkle {
    0%, 100% { 
        opacity: 0; 
        transform: scale(0.5) rotate(0deg);
    }
    50% { 
        opacity: 1; 
        transform: scale(1) rotate(180deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-title {
        font-size: 3rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .envelope {
        width: 160px;
        height: 112px;
    }
    
    .envelope-body {
        height: 80px;
    }
    
    .envelope-flap {
        height: 48px;
    }
    
    .letter-graphic {
        width: 40px;
        height: 60px;
    }
    
    .letter-graphic::before {
        font-size: 1rem;
    }
}

/* Timeline Page Styles */
#timeline-page {
    padding: 4rem 2rem;
    background: var(--gradient-romantic);
}

.timeline-container {
    max-width: 1000px;
    margin: 0 auto;
}

.timeline-title {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    text-align: center;
    color: var(--text-dark);
    margin-bottom: 3rem;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8);
}

.timeline {
    position: relative;
    padding: 2rem 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--accent-gold);
    transform: translateX(-50%);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.timeline-entry {
    position: relative;
    margin: 3rem 0;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.timeline-entry.animate {
    opacity: 1;
    transform: translateY(0);
}

.timeline-entry:nth-child(odd) .timeline-content {
    margin-left: 0;
    margin-right: 55%;
    text-align: right;
}

.timeline-entry:nth-child(even) .timeline-content {
    margin-left: 55%;
    margin-right: 0;
    text-align: left;
}

.timeline-marker {
    position: absolute;
    left: 50%;
    top: 2rem;
    width: 20px;
    height: 20px;
    background: var(--accent-gold);
    border: 4px solid var(--soft-white);
    border-radius: 50%;
    transform: translateX(-50%);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
    z-index: 2;
}

.timeline-content {
    background: var(--soft-white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow-soft);
    border: 2px solid var(--primary-pink);
    position: relative;
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 2rem;
    width: 0;
    height: 0;
    border: 15px solid transparent;
}

.timeline-entry:nth-child(odd) .timeline-content::before {
    right: -30px;
    border-left-color: var(--primary-pink);
}

.timeline-entry:nth-child(even) .timeline-content::before {
    left: -30px;
    border-right-color: var(--primary-pink);
}

.timeline-date {
    font-family: 'Playfair Display', serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--accent-gold);
    margin-bottom: 1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.timeline-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 1rem;
    border: 3px solid var(--secondary-pink);
    transition: transform 0.3s ease;
}

.timeline-image:hover {
    transform: scale(1.05);
}

.timeline-text {
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-dark);
}

/* Proposal Page Styles */
#proposal-page {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: var(--gradient-proposal);
    position: relative;
}

.proposal-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.proposal-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    padding: 2rem;
}

.proposal-question {
    font-family: 'Dancing Script', cursive;
    font-size: 5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 2rem;
    text-shadow: 3px 3px 6px rgba(255, 255, 255, 0.8);
    animation: proposalFadeIn 1.5s ease-out;
}

.ring-animation {
    margin: 2rem 0;
    animation: proposalFadeIn 1.5s ease-out 0.5s both;
}

.ring {
    font-size: 4rem;
    display: inline-block;
    animation: ringSparkle 2s ease-in-out infinite;
}

.yes-button {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    font-weight: 600;
    background: var(--accent-gold);
    color: var(--text-dark);
    border: none;
    padding: 1rem 3rem;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: var(--shadow-medium);
    transition: all 0.3s ease;
    animation: proposalFadeIn 1.5s ease-out 1s both;
    position: relative;
    overflow: hidden;
}

.yes-button:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 20px 50px rgba(255, 215, 0, 0.5);
    background: #ffed4e;
}

.yes-button:active {
    transform: translateY(-2px) scale(1.05);
}

.yes-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.5s ease;
}

.yes-button:hover::before {
    width: 300px;
    height: 300px;
}

.proposal-hearts {
    animation: proposalHearts 1s ease-out 1.5s both;
}

.proposal-sparkles {
    animation: proposalSparkles 1s ease-out 2s both;
}

.celebration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* Proposal Animations */
@keyframes proposalFadeIn {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes ringSparkle {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        filter: brightness(1);
    }
    25% {
        transform: rotate(-10deg) scale(1.1);
        filter: brightness(1.2);
    }
    75% {
        transform: rotate(10deg) scale(1.1);
        filter: brightness(1.2);
    }
}

@keyframes proposalHearts {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes proposalSparkles {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Mobile Responsive for Timeline and Proposal */
@media (max-width: 768px) {
    .timeline::before {
        left: 30px;
    }

    .timeline-entry:nth-child(odd) .timeline-content,
    .timeline-entry:nth-child(even) .timeline-content {
        margin-left: 70px;
        margin-right: 0;
        text-align: left;
    }

    .timeline-marker {
        left: 30px;
    }

    .timeline-entry:nth-child(odd) .timeline-content::before,
    .timeline-entry:nth-child(even) .timeline-content::before {
        left: -30px;
        border-right-color: var(--primary-pink);
        border-left-color: transparent;
    }

    .timeline-title {
        font-size: 2.5rem;
    }

    .proposal-question {
        font-size: 3.5rem;
    }

    .yes-button {
        font-size: 1.5rem;
        padding: 0.8rem 2rem;
    }
}

@media (max-width: 480px) {
    .landing-content {
        padding: 1rem;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .envelope {
        width: 140px;
        height: 98px;
    }

    #timeline-page {
        padding: 2rem 1rem;
    }

    .timeline-content {
        padding: 1.5rem;
        margin-left: 50px !important;
    }

    .timeline-title {
        font-size: 2rem;
    }

    .proposal-question {
        font-size: 2.5rem;
    }

    .ring {
        font-size: 3rem;
    }

    .yes-button {
        font-size: 1.2rem;
        padding: 0.7rem 1.5rem;
    }
}
